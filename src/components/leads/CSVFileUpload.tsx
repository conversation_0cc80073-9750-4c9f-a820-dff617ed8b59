import React, { useState, useRef, useCallback } from 'react';
import { Upload, FileText, AlertCircle, CheckCircle, X, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

interface CSVFileUploadProps {
  onFileSelect: (file: File) => Promise<void>;
  isUploading?: boolean;
  uploadProgress?: number;
  className?: string;
  maxFileSize?: number; // in MB
  disabled?: boolean;
}

interface FileValidationResult {
  valid: boolean;
  error?: string;
  warnings?: string[];
}

export const CSVFileUpload: React.FC<CSVFileUploadProps> = ({
  onFileSelect,
  isUploading = false,
  uploadProgress = 0,
  className,
  maxFileSize = 5,
  disabled = false
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [validationResult, setValidationResult] = useState<FileValidationResult | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  /**
   * Validates CSV file according to our requirements
   */
  const validateCSVFile = useCallback((file: File): FileValidationResult => {
    const warnings: string[] = [];

    // Check file type
    const validTypes = ['text/csv', 'application/csv', 'text/plain'];
    const validExtensions = ['.csv'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    if (!validTypes.includes(file.type) && !validExtensions.includes(fileExtension)) {
      return {
        valid: false,
        error: 'סוג קובץ לא תקין. יש להעלות קובץ CSV בלבד.'
      };
    }

    // Check file size (convert MB to bytes)
    const maxSizeBytes = maxFileSize * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return {
        valid: false,
        error: `גודל הקובץ חורג מהמותר. מקסימום ${maxFileSize}MB.`
      };
    }

    // Check if file is empty
    if (file.size === 0) {
      return {
        valid: false,
        error: 'הקובץ ריק. יש להעלות קובץ CSV עם נתונים.'
      };
    }

    // Add warnings for large files
    if (file.size > 1024 * 1024) { // 1MB
      warnings.push('קובץ גדול - הטעינה עלולה להימשך זמן רב');
    }

    return {
      valid: true,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }, [maxFileSize]);

  /**
   * Handles file selection from input or drag & drop
   */
  const handleFileSelection = useCallback(async (file: File) => {
    setSelectedFile(file);

    const validation = validateCSVFile(file);
    setValidationResult(validation);

    if (!validation.valid) {
      toast.error(validation.error);
      return;
    }

    if (validation.warnings) {
      validation.warnings.forEach(warning => {
        toast.warning(warning);
      });
    }

    try {
      await onFileSelect(file);
      toast.success('קובץ CSV נבחר בהצלחה');
    } catch (error) {
      console.error('Error handling file selection:', error);
      toast.error('שגיאה בטעינת הקובץ');
    }
  }, [validateCSVFile, onFileSelect]);

  /**
   * Handles file input change
   */
  const handleFileInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      handleFileSelection(files[0]);
    }
  }, [handleFileSelection]);

  /**
   * Handles drag and drop events
   */
  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    if (!disabled && !isUploading) {
      setIsDragOver(true);
    }
  }, [disabled, isUploading]);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragOver(false);

    if (disabled || isUploading) return;

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      handleFileSelection(files[0]);
    }
  }, [disabled, isUploading, handleFileSelection]);

  /**
   * Opens file picker
   */
  const openFilePicker = useCallback(() => {
    if (!disabled && !isUploading) {
      fileInputRef.current?.click();
    }
  }, [disabled, isUploading]);

  /**
   * Clears selected file
   */
  const clearFile = useCallback(() => {
    setSelectedFile(null);
    setValidationResult(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  /**
   * Formats file size for display
   */
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  return (
    <div className={className}>
      <Card className={`transition-all duration-200 ${
        isDragOver ? 'border-primary bg-primary/5' : 'border-dashed border-2 border-muted-foreground/25'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary/50 cursor-pointer'}`}>
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
            {isUploading ? (
              <Loader2 className="w-6 h-6 text-primary animate-spin" />
            ) : (
              <Upload className="w-6 h-6 text-primary" />
            )}
          </div>
          <CardTitle className="text-lg">העלאת קובץ CSV</CardTitle>
          <CardDescription>
            גרור קובץ CSV לכאן או לחץ לבחירת קובץ
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Drag & Drop Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              isDragOver ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'
            } ${disabled || isUploading ? 'cursor-not-allowed' : 'cursor-pointer hover:border-primary/50'}`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={openFilePicker}
          >
            <div className="space-y-2">
              <Upload className="w-8 h-8 text-muted-foreground mx-auto" />
              <p className="text-sm text-muted-foreground">
                {isUploading ? 'מעלה קובץ...' : 'לחץ כאן או גרור קובץ CSV'}
              </p>
              <p className="text-xs text-muted-foreground">
                מקסימום {maxFileSize}MB • פורמט CSV בלבד
              </p>
            </div>
          </div>

          {/* Hidden File Input */}
          <input
            ref={fileInputRef}
            type="file"
            accept=".csv,text/csv,application/csv"
            onChange={handleFileInputChange}
            className="hidden"
            disabled={disabled || isUploading}
          />

          {/* Upload Progress */}
          {isUploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>מעלה קובץ...</span>
                <span>{Math.round(uploadProgress)}%</span>
              </div>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}

          {/* Selected File Info */}
          {selectedFile && !isUploading && (
            <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
              <div className="flex items-center gap-3">
                <FileText className="w-5 h-5 text-primary" />
                <div>
                  <p className="font-medium text-sm">{selectedFile.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(selectedFile.size)}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {validationResult?.valid && (
                  <Badge variant="secondary" className="text-xs">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    תקין
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFile}
                  disabled={isUploading}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}

          {/* Validation Errors */}
          {validationResult && !validationResult.valid && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {validationResult.error}
              </AlertDescription>
            </Alert>
          )}

          {/* Validation Warnings */}
          {validationResult?.warnings && validationResult.warnings.length > 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {validationResult.warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* File Requirements */}
          <div className="text-xs text-muted-foreground space-y-1">
            <p><strong>דרישות הקובץ:</strong></p>
            <ul className="list-disc list-inside space-y-1 mr-4">
              <li>פורמט: CSV (Comma Separated Values)</li>
              <li>גודל מקסימלי: {maxFileSize}MB</li>
              <li>קידוד: UTF-8 (מומלץ)</li>
              <li>שורה ראשונה: כותרות עמודות</li>
              <li>מקסימום 1000 שורות</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
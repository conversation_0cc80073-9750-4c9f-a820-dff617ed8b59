import React, { useState } from 'react';
import { Download, FileText, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { downloadCSVTemplate, generateValidationRulesText, CSV_TEMPLATE_FIELDS } from '@/utils/csvTemplateGenerator';
import { toast } from 'sonner';

interface CSVTemplateDownloadProps {
  className?: string;
}

export const CSVTemplateDownload: React.FC<CSVTemplateDownloadProps> = ({ className }) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [showValidationRules, setShowValidationRules] = useState(false);

  const handleDownloadTemplate = async () => {
    try {
      setIsDownloading(true);
      downloadCSVTemplate(true); // Use Hebrew headers by default
      toast.success('תבנית CSV הורדה בהצלחה');
    } catch (error) {
      console.error('Error downloading CSV template:', error);
      toast.error('שגיאה בהורדת תבנית CSV');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleDownloadEnglishTemplate = async () => {
    try {
      setIsDownloading(true);
      downloadCSVTemplate(false); // Use English headers
      toast.success('CSV template downloaded successfully');
    } catch (error) {
      console.error('Error downloading CSV template:', error);
      toast.error('Error downloading CSV template');
    } finally {
      setIsDownloading(false);
    }
  };

  const requiredFields = CSV_TEMPLATE_FIELDS.filter(field => field.required);
  const optionalFields = CSV_TEMPLATE_FIELDS.filter(field => !field.required);

  return (
    <div className={className}>
      <Card className="border-dashed border-2 border-muted-foreground/25 hover:border-primary/50 transition-colors">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
            <FileText className="w-6 h-6 text-primary" />
          </div>
          <CardTitle className="text-lg">הורדת תבנית CSV לייבוא לידים</CardTitle>
          <CardDescription>
            הורד תבנית CSV עם דוגמאות נתונים וכותרות מוכנות לשימוש
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Download Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleDownloadTemplate}
              disabled={isDownloading}
              className="flex-1 flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              {isDownloading ? 'מוריד...' : 'הורד תבנית (עברית)'}
            </Button>
            <Button
              onClick={handleDownloadEnglishTemplate}
              disabled={isDownloading}
              variant="outline"
              className="flex-1 flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              {isDownloading ? 'Downloading...' : 'Download Template (English)'}
            </Button>
          </div>

          <Separator />

          {/* Field Information */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Info className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">שדות הקובץ:</span>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium text-destructive mb-2">שדות חובה:</h4>
                <div className="space-y-1">
                  {requiredFields.map(field => (
                    <div key={field.key} className="flex items-center gap-2">
                      <Badge variant="destructive" className="text-xs">חובה</Badge>
                      <span>{field.hebrewHeader}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium text-muted-foreground mb-2">שדות אופציונליים:</h4>
                <div className="space-y-1">
                  {optionalFields.map(field => (
                    <div key={field.key} className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">אופציונלי</Badge>
                      <span className="text-muted-foreground">{field.hebrewHeader}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Validation Rules Dialog */}
          <Dialog open={showValidationRules} onOpenChange={setShowValidationRules}>
            <DialogTrigger asChild>
              <Button variant="ghost" className="w-full flex items-center gap-2">
                <Info className="w-4 h-4" />
                הצג כללי תקינות מפורטים
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>כללי תקינות לייבוא לידים</DialogTitle>
                <DialogDescription>
                  כללים מפורטים לכל שדה בקובץ CSV
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded-lg">
                  {generateValidationRulesText()}
                </pre>
              </div>
            </DialogContent>
          </Dialog>
        </CardContent>
      </Card>
    </div>
  );
};
import React from 'react';
import { CheckCircle, AlertCircle, XCircle, Users, FileText, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ParseResult, ValidationError, DuplicateLead, generateImportSummary } from '@/utils/csvParser';
import { ImportResult } from '@/services/leadImportService';

interface ImportResultsProps {
  parseResult: ParseResult;
  importResult?: ImportResult;
  className?: string;
}

export const ImportResults: React.FC<ImportResultsProps> = ({
  parseResult,
  importResult,
  className
}) => {
  const hasErrors = parseResult.errors.filter(e => e.severity === 'error').length > 0;
  const hasWarnings = parseResult.errors.filter(e => e.severity === 'warning').length > 0;
  const hasDuplicates = parseResult.duplicates.length > 0;
  const hasImportErrors = importResult && importResult.errors.length > 0;

  const getStatusIcon = () => {
    if (hasErrors || hasImportErrors) {
      return <XCircle className="w-6 h-6 text-destructive" />;
    }
    if (hasWarnings || hasDuplicates) {
      return <AlertTriangle className="w-6 h-6 text-yellow-500" />;
    }
    return <CheckCircle className="w-6 h-6 text-green-500" />;
  };

  const getStatusTitle = () => {
    if (hasErrors || hasImportErrors) {
      return 'ייבוא נכשל';
    }
    if (hasWarnings || hasDuplicates) {
      return 'ייבוא הושלם עם אזהרות';
    }
    return 'ייבוא הושלם בהצלחה';
  };

  const getStatusDescription = () => {
    if (hasErrors) {
      return 'נמצאו שגיאות בקובץ שמונעות את הייבוא';
    }
    if (hasImportErrors) {
      return 'נמצאו שגיאות במהלך הייבוא למערכת';
    }
    if (hasWarnings || hasDuplicates) {
      return 'הייבוא הושלם אך נמצאו בעיות שדורשות תשומת לב';
    }
    return 'כל הלידים יובאו בהצלחה למערכת';
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            {getStatusIcon()}
            <div>
              <CardTitle className="text-lg">{getStatusTitle()}</CardTitle>
              <CardDescription>{getStatusDescription()}</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Summary Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-primary">{parseResult.totalRows}</div>
              <div className="text-sm text-muted-foreground">סה"כ שורות</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {importResult?.importedCount || parseResult.summary.successCount}
              </div>
              <div className="text-sm text-muted-foreground">יובאו בהצלחה</div>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {parseResult.summary.duplicateCount}
              </div>
              <div className="text-sm text-muted-foreground">כפולים</div>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {parseResult.summary.errorCount + (importResult?.errorCount || 0)}
              </div>
              <div className="text-sm text-muted-foreground">שגיאות</div>
            </div>
          </div>

          {/* Import Summary */}
          {importResult && (
            <>
              <Separator />
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  תוצאות ייבוא
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      {importResult.importedCount} יובאו
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                      {importResult.skippedCount} נדלגו
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="destructive">
                      {importResult.errorCount} שגיאות
                    </Badge>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Detailed Summary */}
          <Separator />
          <div>
            <h4 className="font-medium mb-3 flex items-center gap-2">
              <FileText className="w-4 h-4" />
              סיכום מפורט
            </h4>
            <div className="bg-muted p-4 rounded-lg">
              <pre className="text-sm whitespace-pre-wrap font-mono">
                {generateImportSummary(parseResult)}
              </pre>
            </div>
          </div>

          {/* Validation Errors */}
          {parseResult.errors.length > 0 && (
            <>
              <Separator />
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  שגיאות ואזהרות ({parseResult.errors.length})
                </h4>
                <ScrollArea className="h-48 w-full border rounded-lg p-4">
                  <div className="space-y-2">
                    {parseResult.errors.map((error, index) => (
                      <Alert
                        key={index}
                        variant={error.severity === 'error' ? 'destructive' : 'default'}
                        className="text-sm"
                      >
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          <span className="font-medium">שורה {error.row}:</span> {error.field} - {error.message}
                          {error.value && (
                            <span className="text-muted-foreground"> (ערך: "{error.value}")</span>
                          )}
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </>
          )}

          {/* Import Errors */}
          {importResult && importResult.errors.length > 0 && (
            <>
              <Separator />
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <XCircle className="w-4 h-4" />
                  שגיאות ייבוא ({importResult.errors.length})
                </h4>
                <ScrollArea className="h-32 w-full border rounded-lg p-4">
                  <div className="space-y-2">
                    {importResult.errors.map((error, index) => (
                      <Alert key={index} variant="destructive" className="text-sm">
                        <XCircle className="h-4 w-4" />
                        <AlertDescription>{error}</AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </>
          )}

          {/* Duplicate Leads */}
          {parseResult.duplicates.length > 0 && (
            <>
              <Separator />
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  לידים כפולים ({parseResult.duplicates.length})
                </h4>
                <ScrollArea className="h-32 w-full border rounded-lg p-4">
                  <div className="space-y-2">
                    {parseResult.duplicates.map((duplicate, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                        <div>
                          <span className="font-medium">{duplicate.full_name}</span>
                          <span className="text-muted-foreground ml-2">({duplicate.phone})</span>
                        </div>
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                          שורה {duplicate.row}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
/**
 * Tests for CSV File Upload Component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CSVFileUpload } from '../CSVFileUpload';
import { toast } from 'sonner';

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
  },
}));

describe('CSVFileUpload', () => {
  const mockOnFileSelect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders upload area correctly', () => {
    render(<CSVFileUpload onFileSelect={mockOnFileSelect} />);

    expect(screen.getByText('העלאת קובץ CSV')).toBeInTheDocument();
    expect(screen.getByText('גרור קובץ CSV לכאן או לחץ לבחירת קובץ')).toBeInTheDocument();
    expect(screen.getByText('מקסימום 5MB • פורמט CSV בלבד')).toBeInTheDocument();
  });

  it('shows file requirements', () => {
    render(<CSVFileUpload onFileSelect={mockOnFileSelect} />);

    expect(screen.getByText('דרישות הקובץ:')).toBeInTheDocument();
    expect(screen.getByText('פורמט: CSV (Comma Separated Values)')).toBeInTheDocument();
    expect(screen.getByText('גודל מקסימלי: 5MB')).toBeInTheDocument();
    expect(screen.getByText('מקסימום 1000 שורות')).toBeInTheDocument();
  });

  it('validates file type correctly', async () => {
    render(<CSVFileUpload onFileSelect={mockOnFileSelect} />);

    const fileInput = screen.getByRole('button', { hidden: true });
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });

    // Simulate file selection
    Object.defineProperty(fileInput, 'files', {
      value: [invalidFile],
      writable: false,
    });

    fireEvent.change(fileInput);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('סוג קובץ לא תקין. יש להעלות קובץ CSV בלבד.');
    });
  });

  it('validates file size correctly', async () => {
    render(<CSVFileUpload onFileSelect={mockOnFileSelect} maxFileSize={1} />);

    const fileInput = screen.getByRole('button', { hidden: true });
    // Create a file larger than 1MB
    const largeFile = new File(['x'.repeat(2 * 1024 * 1024)], 'large.csv', { type: 'text/csv' });

    Object.defineProperty(fileInput, 'files', {
      value: [largeFile],
      writable: false,
    });

    fireEvent.change(fileInput);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('גודל הקובץ חורג מהמותר. מקסימום 1MB.');
    });
  });

  it('accepts valid CSV file', async () => {
    render(<CSVFileUpload onFileSelect={mockOnFileSelect} />);

    const fileInput = screen.getByRole('button', { hidden: true });
    const validFile = new File(['name,phone\nJohn,123456789'], 'valid.csv', { type: 'text/csv' });

    Object.defineProperty(fileInput, 'files', {
      value: [validFile],
      writable: false,
    });

    fireEvent.change(fileInput);

    await waitFor(() => {
      expect(mockOnFileSelect).toHaveBeenCalledWith(validFile);
      expect(toast.success).toHaveBeenCalledWith('קובץ CSV נבחר בהצלחה');
    });
  });

  it('shows upload progress when uploading', () => {
    render(
      <CSVFileUpload
        onFileSelect={mockOnFileSelect}
        isUploading={true}
        uploadProgress={50}
      />
    );

    expect(screen.getByText('מעלה קובץ...')).toBeInTheDocument();
    expect(screen.getByText('50%')).toBeInTheDocument();
  });

  it('disables interaction when disabled', () => {
    render(<CSVFileUpload onFileSelect={mockOnFileSelect} disabled={true} />);

    const uploadArea = screen.getByText('לחץ כאן או גרור קובץ CSV').closest('div');
    expect(uploadArea).toHaveClass('cursor-not-allowed');
  });

  it('handles drag and drop events', () => {
    render(<CSVFileUpload onFileSelect={mockOnFileSelect} />);

    const dropArea = screen.getByText('לחץ כאן או גרור קובץ CSV').closest('div');

    // Test drag over
    fireEvent.dragOver(dropArea!);
    expect(dropArea).toHaveClass('border-primary');

    // Test drag leave
    fireEvent.dragLeave(dropArea!);
    expect(dropArea).not.toHaveClass('border-primary');
  });
});
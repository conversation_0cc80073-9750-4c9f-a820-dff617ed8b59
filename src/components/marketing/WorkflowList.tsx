import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Edit,
  Trash2,
  Play,
  Pause,
  MessageSquare,
  Clock,
  ArrowRight,
  FileText,
  Loader2
} from 'lucide-react';
import { Workflow } from '@/hooks/useWorkflows';

interface WorkflowListProps {
  workflows: Workflow[];
  isLoading: boolean;
  onEdit: (workflow: Workflow) => void;
  onDelete: (workflowId: string) => void;
  onToggle: (workflowId: string) => void;
}

export const WorkflowList = ({ 
  workflows, 
  isLoading, 
  onEdit, 
  onDelete, 
  onToggle 
}: WorkflowListProps) => {
  
  const getTriggerText = (workflow: Workflow) => {
    const { trigger_type, trigger_config } = workflow;

    if (trigger_type === 'manual') {
      const statusCount = trigger_config.target_statuses?.length || 0;
      return `טריגר ידני: ${statusCount} סטטוסים`;
    }

    const entityType = trigger_type === 'lead_status_change' ? 'ליד' : 'תיק';
    const fromStatus = trigger_config.from_status === 'any' ? 'כל סטטוס' : trigger_config.from_status;

    return `${entityType}: ${fromStatus} → ${trigger_config.to_status}`;
  };

  const getStepIcon = (stepType: string) => {
    switch (stepType) {
      case 'send_whatsapp':
        return <MessageSquare className="h-4 w-4" />;
      case 'wait':
        return <Clock className="h-4 w-4" />;
      case 'update_lead_status':
      case 'update_case_status':
        return <ArrowRight className="h-4 w-4" />;
      case 'create_case':
        return <FileText className="h-4 w-4" />;
      default:
        return <div className="h-4 w-4" />;
    }
  };

  const getStepText = (stepType: string) => {
    switch (stepType) {
      case 'send_whatsapp':
        return 'שלח וואטסאפ';
      case 'wait':
        return 'המתן';
      case 'update_lead_status':
        return 'עדכן סטטוס ליד';
      case 'update_case_status':
        return 'עדכן סטטוס תיק';
      case 'create_case':
        return 'צור תיק';
      default:
        return stepType;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="mr-2">טוען זרימות עבודה...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (workflows.length === 0) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="text-center">
            <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4">
              <MessageSquare className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">אין זרימות עבודה</h3>
            <p className="text-muted-foreground mb-4">
              צור את זרימת העבודה הראשונה שלך כדי להתחיל עם אוטומציה שיווקית
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {workflows.map((workflow) => (
        <Card key={workflow.id} className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex flex-col">
                  <CardTitle className="text-lg">{workflow.name}</CardTitle>
                  <span className="text-xs text-muted-foreground font-mono">
                    ID: {workflow.id}
                  </span>
                </div>
                <Badge variant={workflow.is_active ? "default" : "secondary"}>
                  {workflow.is_active ? 'פעיל' : 'מושבת'}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onToggle(workflow.id)}
                  className="h-8 w-8 p-0"
                >
                  {workflow.is_active ? (
                    <Pause className="h-4 w-4" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(workflow)}
                  className="h-8 w-8 p-0"
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(workflow.id)}
                  className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
            {workflow.description && (
              <p className="text-sm text-muted-foreground">{workflow.description}</p>
            )}
          </CardHeader>
          
          <CardContent className="pt-0">
            <div className="space-y-4">
              {/* Trigger */}
              <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                  <ArrowRight className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="text-sm font-medium">טריגר</p>
                  <p className="text-xs text-muted-foreground">{getTriggerText(workflow)}</p>
                </div>
              </div>

              {/* Steps */}
              {workflow.workflow_steps && workflow.workflow_steps.length > 0 && (
                <div>
                  <p className="text-sm font-medium mb-2">שלבים ({workflow.workflow_steps.length})</p>
                  <div className="flex flex-wrap gap-2">
                    {workflow.workflow_steps
                      .sort((a, b) => a.step_order - b.step_order)
                      .slice(0, 5) // Show first 5 steps
                      .map((step, index) => (
                        <div
                          key={step.id}
                          className="flex items-center gap-1 px-2 py-1 bg-background border rounded text-xs"
                        >
                          {getStepIcon(step.step_type)}
                          <span>{index + 1}. {getStepText(step.step_type)}</span>
                        </div>
                      ))}
                    {workflow.workflow_steps.length > 5 && (
                      <div className="flex items-center px-2 py-1 text-xs text-muted-foreground">
                        +{workflow.workflow_steps.length - 5} נוספים
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Metadata */}
              <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
                <span>נוצר: {new Date(workflow.created_at).toLocaleDateString('he-IL')}</span>
                <span>עודכן: {new Date(workflow.updated_at).toLocaleDateString('he-IL')}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

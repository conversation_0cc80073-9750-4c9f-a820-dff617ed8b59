import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Plus, FolderOpen, FileSpreadsheet } from "lucide-react";
import { LeadModal } from "@/components/office/LeadModal";
import { LeadsList } from "@/components/leads/LeadsList";
import { ActiveCallBar } from "@/components/leads/ActiveCallBar";
import { CSVImportModal } from "@/components/leads/CSVImportModal";
import { useLeads } from "@/hooks/useLeads";
import { useCallManager } from "@/hooks/useCallManager";
import { Lead } from "@/components/leads/LeadCard";
import { ParseResult } from "@/utils/csvParser";
import { ImportResult, processCSVImport, getUserImportContext } from "@/services/leadImportService";
import { useAuth } from "@/hooks/useAuth";
import { useCompany } from "@/contexts/CompanyContext";
import { toast } from 'sonner';

export default function LeadsPage() {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingLead, setEditingLead] = useState<Lead | null>(null);
  const [showCSVImport, setShowCSVImport] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [parseResult, setParseResult] = useState<ParseResult | undefined>();
  const [importResult, setImportResult] = useState<ImportResult | undefined>();
  const [progressMessage, setProgressMessage] = useState<string>('');
  const { leads, isLoading, addLead, updateLead, deleteLead, refetchLeads } = useLeads();
  const { user } = useAuth();
  const { currentCompany } = useCompany();
  const {
    activeCall,
    isLoading: isCallLoading,
    initiateCall,
    hangupCall,
    muteCall,
    unmuteCall,
    acceptCall,
    rejectCall,
    isMuted,
    device,
    voiceCall
  } = useCallManager();

  const handleEdit = (lead: Lead) => {
    setEditingLead(lead);
    setIsModalOpen(true);
  };

  const handleAddLead = async (leadData: Omit<Lead, 'id' | 'created_at' | 'updated_at'>) => {
    return await addLead(leadData);
  };

  const handleUpdateLead = async (leadId: string, updates: Partial<Lead>) => {
    return await updateLead(leadId, updates);
  };

  const handleWhatsApp = (phoneNumber: string) => {
    // Find the lead by phone number to get the lead ID
    const lead = leads.find(l => l.phone === phoneNumber);
    if (lead) {
      navigate(`/office/whatsapp?lead=${lead.id}`);
    }
  };

  /**
   * Handles CSV file selection and processing
   */
  const handleCSVFileSelect = async (file: File) => {
    if (!user || !currentCompany) {
      toast.error('שגיאה באימות משתמש');
      return;
    }

    try {
      setIsUploading(true);
      setUploadProgress(0);
      setParseResult(undefined);
      setImportResult(undefined);

      // Get user import context
      const context = await getUserImportContext(user.id, currentCompany.id);
      if (!context) {
        toast.error('שגיאה בהגדרת הקשר ייבוא');
        return;
      }

      // Process CSV import with progress tracking
      const result = await processCSVImport(file, context, (progress, message) => {
        setUploadProgress(progress);
        setProgressMessage(message);
      });

      setParseResult(result.parseResult);
      setImportResult(result.importResult);

      // Show appropriate toast based on results
      if (result.parseResult.success && result.importResult?.success) {
        toast.success(`ייבוא הושלם בהצלחה! ${result.importResult.importedCount} לידים יובאו`);
        // Refresh leads list
        refetchLeads();
      } else if (result.parseResult.summary.errorCount > 0) {
        toast.error('נמצאו שגיאות בקובץ CSV');
      } else if (result.importResult && !result.importResult.success) {
        toast.error('שגיאה בייבוא לידים למערכת');
      } else {
        toast.warning('ייבוא הושלם עם אזהרות');
      }

    } catch (error) {
      console.error('Error processing CSV file:', error);
      toast.error(`שגיאה בעיבוד קובץ CSV: ${error instanceof Error ? error.message : 'שגיאה לא ידועה'}`);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      setProgressMessage('');
    }
  };

  /**
   * Handles import completion
   */
  const handleImportComplete = () => {
    setShowCSVImport(false);
    setParseResult(undefined);
    setImportResult(undefined);
    // Refresh leads list to show new imports
    refetchLeads();
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">טוען לידים...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {activeCall && (
        <ActiveCallBar
          activeCall={activeCall}
          onHangup={hangupCall}
          onMute={muteCall}
          onUnmute={unmuteCall}
          onAccept={acceptCall}
          onReject={rejectCall}
          isMuted={isMuted}
        />
      )}

      <div className={`flex items-center justify-between ${activeCall ? 'mt-20' : ''}`}>
        <div>
          <h1 className="text-2xl font-bold text-foreground">לידים</h1>
          <p className="text-muted-foreground">ניהול לידים ולקוחות פוטנציאליים ({leads.length} לידים)</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => setShowCSVImport(true)}
          >
            <FileSpreadsheet className="w-4 h-4" />
            ייבוא CSV
          </Button>

          <Button
            className="btn-professional flex items-center gap-2"
            onClick={() => setIsModalOpen(true)}
          >
            <Plus className="w-4 h-4" />
            הוסף ליד חדש
          </Button>
        </div>
      </div>

      <LeadsList
        leads={leads}
        onCall={initiateCall}
        onEdit={handleEdit}
        onDelete={deleteLead}
        onWhatsApp={handleWhatsApp}
        isCallLoading={isCallLoading}
        onRefresh={refetchLeads}
      />

      <LeadModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingLead(null);
        }}
        editingLead={editingLead}
        onAddLead={handleAddLead}
        onUpdateLead={handleUpdateLead}
      />

      <CSVImportModal
        open={showCSVImport}
        onOpenChange={setShowCSVImport}
        onFileSelect={handleCSVFileSelect}
        isUploading={isUploading}
        uploadProgress={uploadProgress}
        parseResult={parseResult}
        importResult={importResult}
        onImportComplete={handleImportComplete}
      />
    </div>
  );
}
/**
 * Lead Import Service
 * Handles lead validation, company context, and bulk import operations
 */

import { supabase } from '@/integrations/supabase/client';
import { ParsedLead, ParseResult, parseCSVFile } from '@/utils/csvParser';

export interface ImportContext {
  userId: string;
  companyId: string;
  userRole: 'super_admin' | 'company_admin' | 'user';
}

export interface ImportOptions {
  batchSize?: number;
  validateUsers?: boolean;
  skipDuplicates?: boolean;
}

export interface ImportResult {
  success: boolean;
  importedCount: number;
  skippedCount: number;
  errorCount: number;
  errors: string[];
  importedLeads: any[];
}

/**
 * Gets existing phone numbers for duplicate detection
 */
export async function getExistingPhones(companyId: string): Promise<string[]> {
  try {
    const { data, error } = await supabase
      .from('leads')
      .select('phone')
      .eq('company_id', companyId);

    if (error) {
      console.error('Error fetching existing phones:', error);
      return [];
    }

    return data?.map(lead => lead.phone) || [];
  } catch (error) {
    console.error('Error in getExistingPhones:', error);
    return [];
  }
}

/**
 * Validates assigned user emails against existing users in the company
 */
export async function validateAssignedUsers(
  leads: ParsedLead[],
  companyId: string
): Promise<{ validatedLeads: ParsedLead[]; errors: string[] }> {
  const errors: string[] = [];
  const validatedLeads: ParsedLead[] = [];

  // Get all unique assigned user emails
  const assignedEmails = [...new Set(
    leads
      .map(lead => lead.assigned_user_email)
      .filter(email => email && email.trim())
  )];

  if (assignedEmails.length === 0) {
    return { validatedLeads: leads, errors };
  }

  try {
    // Query users in the company
    const { data: users, error } = await supabase
      .from('user_roles')
      .select(`
        user_id,
        users!inner(email)
      `)
      .eq('company_id', companyId)
      .in('users.email', assignedEmails);

    if (error) {
      console.error('Error validating assigned users:', error);
      errors.push('שגיאה בבדיקת משתמשים מוקצים');
      return { validatedLeads: leads, errors };
    }

    const validEmails = new Set(users?.map(u => u.users.email) || []);
    const emailToUserId = new Map(
      users?.map(u => [u.users.email, u.user_id]) || []
    );

    // Validate each lead
    leads.forEach((lead, index) => {
      if (lead.assigned_user_email) {
        if (!validEmails.has(lead.assigned_user_email)) {
          errors.push(
            `שורה ${index + 2}: משתמש מוקצה "${lead.assigned_user_email}" לא נמצא בחברה`
          );
          // Remove invalid assigned user email but keep the lead
          const { assigned_user_email, ...leadWithoutAssignment } = lead;
          validatedLeads.push(leadWithoutAssignment);
        } else {
          validatedLeads.push(lead);
        }
      } else {
        validatedLeads.push(lead);
      }
    });

    return { validatedLeads, errors };
  } catch (error) {
    console.error('Error in validateAssignedUsers:', error);
    errors.push('שגיאה בבדיקת משתמשים מוקצים');
    return { validatedLeads: leads, errors };
  }
}

/**
 * Converts assigned user emails to user IDs
 */
export async function resolveAssignedUserIds(
  leads: ParsedLead[],
  companyId: string
): Promise<ParsedLead[]> {
  const assignedEmails = [...new Set(
    leads
      .map(lead => lead.assigned_user_email)
      .filter(email => email && email.trim())
  )];

  if (assignedEmails.length === 0) {
    return leads;
  }

  try {
    const { data: users, error } = await supabase
      .from('user_roles')
      .select(`
        user_id,
        users!inner(email)
      `)
      .eq('company_id', companyId)
      .in('users.email', assignedEmails);

    if (error) {
      console.error('Error resolving user IDs:', error);
      return leads;
    }

    const emailToUserId = new Map(
      users?.map(u => [u.users.email, u.user_id]) || []
    );

    return leads.map(lead => {
      if (lead.assigned_user_email && emailToUserId.has(lead.assigned_user_email)) {
        const { assigned_user_email, ...leadData } = lead;
        return {
          ...leadData,
          assigned_user_id: emailToUserId.get(assigned_user_email)
        } as any;
      }
      return lead;
    });
  } catch (error) {
    console.error('Error in resolveAssignedUserIds:', error);
    return leads;
  }
}

/**
 * Imports leads in batches with proper error handling
 */
export async function importLeadsBatch(
  leads: ParsedLead[],
  context: ImportContext,
  options: ImportOptions = {}
): Promise<ImportResult> {
  const { batchSize = 50 } = options;
  const result: ImportResult = {
    success: false,
    importedCount: 0,
    skippedCount: 0,
    errorCount: 0,
    errors: [],
    importedLeads: []
  };

  try {
    // Resolve assigned user IDs
    const leadsWithUserIds = await resolveAssignedUserIds(leads, context.companyId);

    // Process in batches
    for (let i = 0; i < leadsWithUserIds.length; i += batchSize) {
      const batch = leadsWithUserIds.slice(i, i + batchSize);

      // Prepare leads for insertion
      const leadsToInsert = batch.map(lead => ({
        full_name: lead.full_name,
        phone: lead.phone,
        email: lead.email || null,
        source: lead.source || null,
        status: lead.status || 'ליד חדש',
        value: lead.value || null,
        notes: lead.notes || null,
        user_id: context.userId,
        company_id: context.companyId,
        assigned_user_id: (lead as any).assigned_user_id || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      // Insert batch
      const { data, error } = await supabase
        .from('leads')
        .insert(leadsToInsert)
        .select();

      if (error) {
        console.error('Error inserting leads batch:', error);
        result.errors.push(`שגיאה בייבוא קבוצה ${Math.floor(i / batchSize) + 1}: ${error.message}`);
        result.errorCount += batch.length;
      } else {
        result.importedCount += data?.length || 0;
        result.importedLeads.push(...(data || []));
      }
    }

    result.success = result.errorCount === 0 && result.importedCount > 0;
    return result;
  } catch (error) {
    console.error('Error in importLeadsBatch:', error);
    result.errors.push(`שגיאה כללית בייבוא: ${error instanceof Error ? error.message : 'שגיאה לא ידועה'}`);
    result.errorCount = leads.length;
    return result;
  }
}

/**
 * Main function to process CSV file and import leads
 */
export async function processCSVImport(
  file: File,
  context: ImportContext,
  onProgress?: (progress: number, message: string) => void
): Promise<{ parseResult: ParseResult; importResult?: ImportResult }> {
  try {
    // Step 1: Parse CSV file
    onProgress?.(10, 'מתחיל עיבוד קובץ CSV...');

    const existingPhones = await getExistingPhones(context.companyId);
    onProgress?.(20, 'בודק לידים קיימים...');

    const parseResult = await parseCSVFile(file, {
      maxRows: 1000,
      existingPhones
    });

    onProgress?.(50, 'מסיים עיבוד קובץ CSV...');

    if (!parseResult.success || parseResult.data.length === 0) {
      return { parseResult };
    }

    // Step 2: Validate assigned users
    onProgress?.(60, 'מאמת משתמשים מוקצים...');

    const { validatedLeads, errors: userValidationErrors } = await validateAssignedUsers(
      parseResult.data,
      context.companyId
    );

    // Add user validation errors to parse result
    if (userValidationErrors.length > 0) {
      parseResult.errors.push(...userValidationErrors.map(error => ({
        row: 0,
        field: 'משתמש מוקצה',
        value: '',
        message: error,
        severity: 'warning' as const
      })));
      parseResult.summary.warningCount += userValidationErrors.length;
    }

    // Step 3: Import leads
    onProgress?.(70, 'מייבא לידים...');

    const importResult = await importLeadsBatch(validatedLeads, context, {
      batchSize: 50,
      validateUsers: true,
      skipDuplicates: true
    });

    onProgress?.(100, 'ייבוא הושלם בהצלחה!');

    return { parseResult, importResult };
  } catch (error) {
    console.error('Error in processCSVImport:', error);
    throw new Error(`שגיאה בעיבוד הייבוא: ${error instanceof Error ? error.message : 'שגיאה לא ידועה'}`);
  }
}

/**
 * Gets user context for import operations
 */
export async function getUserImportContext(userId: string, selectedCompanyId?: string): Promise<ImportContext | null> {
  try {
    const { data: userRole, error } = await supabase
      .from('user_roles')
      .select('role, company_id')
      .eq('user_id', userId)
      .single();

    if (error || !userRole) {
      console.error('Error getting user role:', error);
      return null;
    }

    // For super admin, use selected company or their default company
    if (userRole.role === 'super_admin') {
      const companyId = selectedCompanyId || userRole.company_id;
      return {
        userId,
        companyId,
        userRole: 'super_admin'
      };
    }

    // For regular users, use their assigned company
    return {
      userId,
      companyId: userRole.company_id,
      userRole: userRole.role as 'company_admin' | 'user'
    };
  } catch (error) {
    console.error('Error in getUserImportContext:', error);
    return null;
  }
}
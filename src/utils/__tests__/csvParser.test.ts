/**
 * Tests for CSV Parser and Validator
 */

import {
  validateIsraeliPhone,
  validateEmail,
  validateLeadSource,
  validateLeadStatus,
  validateLeadValue,
  parseCSVFile,
  formatValidationErrors,
  generateImportSummary
} from '../csvParser';

describe('CSV Parser Validators', () => {
  describe('validateIsraeliPhone', () => {
    it('validates correct Israeli mobile numbers', () => {
      const validNumbers = [
        '0501234567',
        '************',
        '************',
        '+972501234567',
        '972501234567'
      ];

      validNumbers.forEach(phone => {
        const result = validateIsraeliPhone(phone);
        expect(result.valid).toBe(true);
        expect(result.normalized).toBe('0501234567');
      });
    });

    it('validates correct Israeli landline numbers', () => {
      const validNumbers = [
        '0212345678',
        '02-123-4567',
        '03-123-4567',
        '04-123-4567'
      ];

      validNumbers.forEach(phone => {
        const result = validateIsraeliPhone(phone);
        expect(result.valid).toBe(true);
      });
    });

    it('rejects invalid phone numbers', () => {
      const invalidNumbers = [
        '123456789',
        '0601234567', // Invalid prefix
        '050123456',  // Too short
        '05012345678', // Too long
        'abc123',
        ''
      ];

      invalidNumbers.forEach(phone => {
        const result = validateIsraeliPhone(phone);
        expect(result.valid).toBe(false);
        expect(result.error).toBeDefined();
      });
    });
  });

  describe('validateEmail', () => {
    it('validates correct email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        const result = validateEmail(email);
        expect(result.valid).toBe(true);
      });
    });

    it('allows empty email (optional field)', () => {
      const result = validateEmail('');
      expect(result.valid).toBe(true);
    });

    it('rejects invalid email addresses', () => {
      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'user@',
        'user@domain',
        'user <EMAIL>'
      ];

      invalidEmails.forEach(email => {
        const result = validateEmail(email);
        expect(result.valid).toBe(false);
        expect(result.error).toBeDefined();
      });
    });
  });

  describe('validateLeadSource', () => {
    it('validates known lead sources', () => {
      const validSources = ['גוגל', 'פייסבוק', 'המלצה'];

      validSources.forEach(source => {
        const result = validateLeadSource(source);
        expect(result.valid).toBe(true);
        expect(result.normalized).toBe(source);
      });
    });

    it('allows empty source (optional field)', () => {
      const result = validateLeadSource('');
      expect(result.valid).toBe(true);
    });

    it('rejects unknown lead sources', () => {
      const result = validateLeadSource('מקור לא ידוע');
      expect(result.valid).toBe(false);
      expect(result.error).toContain('מקור לא מוכר');
    });
  });

  describe('validateLeadStatus', () => {
    it('validates known lead statuses', () => {
      const validStatuses = ['ליד חדש', 'צריך פולואפ', 'לקוח סגור'];

      validStatuses.forEach(status => {
        const result = validateLeadStatus(status);
        expect(result.valid).toBe(true);
        expect(result.normalized).toBe(status);
      });
    });

    it('defaults to "ליד חדש" for empty status', () => {
      const result = validateLeadStatus('');
      expect(result.valid).toBe(true);
      expect(result.normalized).toBe('ליד חדש');
    });

    it('rejects unknown lead statuses', () => {
      const result = validateLeadStatus('סטטוס לא ידוע');
      expect(result.valid).toBe(false);
      expect(result.error).toContain('סטטוס לא מוכר');
    });
  });

  describe('validateLeadValue', () => {
    it('validates numeric values', () => {
      const validValues = ['1000', '1000.50', '0', 1500];

      validValues.forEach(value => {
        const result = validateLeadValue(value);
        expect(result.valid).toBe(true);
        expect(typeof result.normalized).toBe('number');
      });
    });

    it('allows empty value (optional field)', () => {
      const result = validateLeadValue('');
      expect(result.valid).toBe(true);
    });

    it('rejects negative values', () => {
      const result = validateLeadValue('-100');
      expect(result.valid).toBe(false);
      expect(result.error).toContain('ערך חייב להיות חיובי');
    });

    it('rejects non-numeric values', () => {
      const result = validateLeadValue('not a number');
      expect(result.valid).toBe(false);
      expect(result.error).toContain('ערך לא תקין');
    });
  });
});
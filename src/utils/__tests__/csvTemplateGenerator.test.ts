/**
 * Tests for CSV Template Generator
 */

import {
  generateCSVTemplate,
  generateCSVTemplateWithHebrewHeaders,
  generateValidationRulesText,
  CSV_TEMPLATE_FIELDS
} from '../csvTemplateGenerator';

describe('CSV Template Generator', () => {
  describe('generateCSVTemplate', () => {
    it('should generate CSV with English headers', () => {
      const csv = generateCSVTemplate();

      // Check that it contains the expected headers
      expect(csv).toContain('full_name,phone,email,source,status,value,notes,assigned_user_email');

      // Check that it contains sample data
      expect(csv).toContain('יוחנן כהן');
      expect(csv).toContain('0501234567');
      expect(csv).toContain('<EMAIL>');
    });

    it('should properly escape CSV values with commas', () => {
      const csv = generateCSVTemplate();

      // Values with commas should be wrapped in quotes
      expect(csv).toMatch(/"[^"]*,[^"]*"/);
    });
  });

  describe('generateCSVTemplateWithHebrewHeaders', () => {
    it('should generate CSV with Hebrew headers', () => {
      const csv = generateCSVTemplateWithHebrewHeaders();

      // Check that it contains Hebrew headers
      expect(csv).toContain('שם מלא');
      expect(csv).toContain('טלפון');
      expect(csv).toContain('אימייל');

      // Check that it contains sample data
      expect(csv).toContain('יוחנן כהן');
      expect(csv).toContain('0501234567');
    });
  });

  describe('generateValidationRulesText', () => {
    it('should generate validation rules documentation', () => {
      const rules = generateValidationRulesText();

      // Check that it contains validation information
      expect(rules).toContain('כללי תקינות לייבוא לידים');
      expect(rules).toContain('שם מלא (חובה)');
      expect(rules).toContain('טלפון (חובה)');
      expect(rules).toContain('מקסימום 1000 לידים');
    });
  });

  describe('CSV_TEMPLATE_FIELDS', () => {
    it('should have required fields defined', () => {
      const requiredFields = CSV_TEMPLATE_FIELDS.filter(field => field.required);

      expect(requiredFields).toHaveLength(2);
      expect(requiredFields.map(f => f.key)).toContain('full_name');
      expect(requiredFields.map(f => f.key)).toContain('phone');
    });

    it('should have all expected fields', () => {
      const fieldKeys = CSV_TEMPLATE_FIELDS.map(field => field.key);

      expect(fieldKeys).toContain('full_name');
      expect(fieldKeys).toContain('phone');
      expect(fieldKeys).toContain('email');
      expect(fieldKeys).toContain('source');
      expect(fieldKeys).toContain('status');
      expect(fieldKeys).toContain('value');
      expect(fieldKeys).toContain('notes');
      expect(fieldKeys).toContain('assigned_user_email');
    });

    it('should have Hebrew headers for all fields', () => {
      CSV_TEMPLATE_FIELDS.forEach(field => {
        expect(field.hebrewHeader).toBeTruthy();
        expect(field.englishHeader).toBeTruthy();
        expect(field.description).toBeTruthy();
        expect(field.example).toBeTruthy();
      });
    });
  });
});
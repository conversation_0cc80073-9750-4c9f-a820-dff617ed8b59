/**
 * CSV Parser and Validator for Lead Import
 * Handles CSV parsing, data validation, and lead processing with Hebrew support
 */

import <PERSON> from 'papaparse';
import { LEAD_STATUSES } from '@/constants/leadStatuses';
import { LEAD_SOURCES } from '@/constants/leadSources';
import { CSV_TEMPLATE_FIELDS } from './csvTemplateGenerator';

export interface ParsedLead {
  full_name: string;
  phone: string;
  email?: string;
  source?: string;
  status?: string;
  value?: number;
  notes?: string;
  assigned_user_email?: string;
}

export interface ValidationError {
  row: number;
  field: string;
  value: any;
  message: string;
  severity: 'error' | 'warning';
}

export interface DuplicateLead {
  row: number;
  phone: string;
  full_name: string;
  existingLeadId?: string;
}

export interface ParseResult {
  success: boolean;
  data: ParsedLead[];
  errors: ValidationError[];
  duplicates: DuplicateLead[];
  totalRows: number;
  validRows: number;
  skippedRows: number;
  summary: {
    successCount: number;
    errorCount: number;
    duplicateCount: number;
    warningCount: number;
  };
}

export interface CSVParseOptions {
  maxRows?: number;
  skipEmptyLines?: boolean;
  trimWhitespace?: boolean;
  existingPhones?: string[]; // For duplicate detection
}

/**
 * Maps CSV headers to our internal field structure
 * Supports both Hebrew and English headers
 */
const HEADER_MAPPING: Record<string, string> = {
  // Hebrew headers
  'שם מלא': 'full_name',
  'טלפון': 'phone',
  'אימייל': 'email',
  'מקור': 'source',
  'סטטוס': 'status',
  'ערך': 'value',
  'הערות': 'notes',
  'אימייל משתמש מוקצה': 'assigned_user_email',

  // English headers
  'full_name': 'full_name',
  'phone': 'phone',
  'email': 'email',
  'source': 'source',
  'status': 'status',
  'value': 'value',
  'notes': 'notes',
  'assigned_user_email': 'assigned_user_email',

  // Alternative English headers
  'name': 'full_name',
  'fullname': 'full_name',
  'full name': 'full_name',
  'telephone': 'phone',
  'mobile': 'phone',
  'phone_number': 'phone',
  'lead_source': 'source',
  'lead_status': 'status',
  'lead_value': 'value',
  'comments': 'notes',
  'assigned_email': 'assigned_user_email',
  'assigned_user': 'assigned_user_email'
};

/**
 * Validates Israeli phone number format
 */
export function validateIsraeliPhone(phone: string): { valid: boolean; normalized?: string; error?: string } {
  if (!phone || typeof phone !== 'string') {
    return { valid: false, error: 'מספר טלפון חסר' };
  }

  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');

  // Handle international format (+972)
  let normalized = cleaned;
  if (normalized.startsWith('972')) {
    normalized = '0' + normalized.substring(3);
  }

  // Check length (Israeli numbers are 9-10 digits starting with 0)
  if (normalized.length < 9 || normalized.length > 10) {
    return { valid: false, error: 'אורך מספר טלפון לא תקין' };
  }

  // Must start with 0
  if (!normalized.startsWith('0')) {
    return { valid: false, error: 'מספר טלפון חייב להתחיל ב-0' };
  }

  // Validate mobile prefixes (050, 052, 053, 054, 055, 058)
  const mobileRegex = /^05[0-8]\d{7}$/;
  // Validate landline prefixes (02, 03, 04, 08, 09)
  const landlineRegex = /^0[2-4,8-9]\d{7,8}$/;

  if (!mobileRegex.test(normalized) && !landlineRegex.test(normalized)) {
    return { valid: false, error: 'קידומת טלפון לא תקינה' };
  }

  return { valid: true, normalized };
}

/**
 * Validates email format
 */
export function validateEmail(email: string): { valid: boolean; error?: string } {
  if (!email || typeof email !== 'string') {
    return { valid: true }; // Email is optional
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email.trim())) {
    return { valid: false, error: 'פורמט אימייל לא תקין' };
  }

  return { valid: true };
}

/**
 * Validates and normalizes lead source
 */
export function validateLeadSource(source: string): { valid: boolean; normalized?: string; error?: string } {
  if (!source || typeof source !== 'string') {
    return { valid: true }; // Source is optional
  }

  const trimmed = source.trim();
  const found = LEAD_SOURCES.find(s => s === trimmed);

  if (!found) {
    return {
      valid: false,
      error: `מקור לא מוכר: "${trimmed}". ערכים מותרים: ${LEAD_SOURCES.join(', ')}`
    };
  }

  return { valid: true, normalized: found };
}

/**
 * Validates and normalizes lead status
 */
export function validateLeadStatus(status: string): { valid: boolean; normalized?: string; error?: string } {
  if (!status || typeof status !== 'string') {
    return { valid: true, normalized: 'ליד חדש' }; // Default status
  }

  const trimmed = status.trim();
  const found = LEAD_STATUSES.find(s => s === trimmed);

  if (!found) {
    return {
      valid: false,
      error: `סטטוס לא מוכר: "${trimmed}". ערכים מותרים: ${LEAD_STATUSES.join(', ')}`
    };
  }

  return { valid: true, normalized: found };
}

/**
 * Validates lead value (monetary)
 */
export function validateLeadValue(value: any): { valid: boolean; normalized?: number; error?: string } {
  if (!value || value === '') {
    return { valid: true }; // Value is optional
  }

  // Convert to string and clean
  const cleaned = String(value).replace(/[^\d.-]/g, '');
  const parsed = parseFloat(cleaned);

  if (isNaN(parsed)) {
    return { valid: false, error: 'ערך לא תקין - חייב להיות מספר' };
  }

  if (parsed < 0) {
    return { valid: false, error: 'ערך חייב להיות חיובי' };
  }

  return { valid: true, normalized: parsed };
}

/**
 * Maps CSV headers to internal field names
 */
function mapHeaders(headers: string[]): Record<string, string> {
  const mapping: Record<string, string> = {};

  headers.forEach((header, index) => {
    const trimmed = header.trim().toLowerCase();
    const mapped = HEADER_MAPPING[trimmed] || HEADER_MAPPING[header.trim()];

    if (mapped) {
      mapping[index.toString()] = mapped;
    }
  });

  return mapping;
}

/**
 * Validates a single lead row
 */
function validateLeadRow(
  row: any,
  rowIndex: number,
  headerMapping: Record<string, string>,
  existingPhones: string[] = []
): { lead?: ParsedLead; errors: ValidationError[]; isDuplicate: boolean } {
  const errors: ValidationError[] = [];
  const lead: Partial<ParsedLead> = {};

  // Extract and validate each field
  Object.entries(headerMapping).forEach(([colIndex, fieldName]) => {
    const value = row[colIndex];

    switch (fieldName) {
      case 'full_name':
        if (!value || typeof value !== 'string' || value.trim().length < 2) {
          errors.push({
            row: rowIndex,
            field: 'שם מלא',
            value,
            message: 'שם מלא חובה ומינימום 2 תווים',
            severity: 'error'
          });
        } else {
          lead.full_name = value.trim();
        }
        break;

      case 'phone':
        const phoneValidation = validateIsraeliPhone(value);
        if (!phoneValidation.valid) {
          errors.push({
            row: rowIndex,
            field: 'טלפון',
            value,
            message: phoneValidation.error || 'מספר טלפון לא תקין',
            severity: 'error'
          });
        } else {
          lead.phone = phoneValidation.normalized!;
        }
        break;

      case 'email':
        if (value && value.trim()) {
          const emailValidation = validateEmail(value);
          if (!emailValidation.valid) {
            errors.push({
              row: rowIndex,
              field: 'אימייל',
              value,
              message: emailValidation.error || 'פורמט אימייל לא תקין',
              severity: 'error'
            });
          } else {
            lead.email = value.trim();
          }
        }
        break;

      case 'source':
        if (value && value.trim()) {
          const sourceValidation = validateLeadSource(value);
          if (!sourceValidation.valid) {
            errors.push({
              row: rowIndex,
              field: 'מקור',
              value,
              message: sourceValidation.error || 'מקור לא תקין',
              severity: 'error'
            });
          } else {
            lead.source = sourceValidation.normalized;
          }
        }
        break;

      case 'status':
        const statusValidation = validateLeadStatus(value);
        if (!statusValidation.valid) {
          errors.push({
            row: rowIndex,
            field: 'סטטוס',
            value,
            message: statusValidation.error || 'סטטוס לא תקין',
            severity: 'error'
          });
        } else {
          lead.status = statusValidation.normalized;
        }
        break;

      case 'value':
        if (value && value !== '') {
          const valueValidation = validateLeadValue(value);
          if (!valueValidation.valid) {
            errors.push({
              row: rowIndex,
              field: 'ערך',
              value,
              message: valueValidation.error || 'ערך לא תקין',
              severity: 'error'
            });
          } else {
            lead.value = valueValidation.normalized;
          }
        }
        break;

      case 'notes':
        if (value && value.trim()) {
          lead.notes = value.trim();
        }
        break;

      case 'assigned_user_email':
        if (value && value.trim()) {
          const emailValidation = validateEmail(value);
          if (!emailValidation.valid) {
            errors.push({
              row: rowIndex,
              field: 'אימייל משתמש מוקצה',
              value,
              message: 'פורמט אימייל לא תקין',
              severity: 'error'
            });
          } else {
            lead.assigned_user_email = value.trim();
          }
        }
        break;
    }
  });

  // Check for duplicates
  const isDuplicate = lead.phone ? existingPhones.includes(lead.phone) : false;

  // Return result
  if (errors.filter(e => e.severity === 'error').length === 0 && lead.full_name && lead.phone) {
    return {
      lead: lead as ParsedLead,
      errors,
      isDuplicate
    };
  }

  return { errors, isDuplicate };
}

/**
 * Main CSV parsing function
 */
export async function parseCSVFile(
  file: File,
  options: CSVParseOptions = {}
): Promise<ParseResult> {
  const {
    maxRows = 1000,
    skipEmptyLines = true,
    trimWhitespace = true,
    existingPhones = []
  } = options;

  return new Promise((resolve) => {
    const result: ParseResult = {
      success: false,
      data: [],
      errors: [],
      duplicates: [],
      totalRows: 0,
      validRows: 0,
      skippedRows: 0,
      summary: {
        successCount: 0,
        errorCount: 0,
        duplicateCount: 0,
        warningCount: 0
      }
    };

    Papa.parse(file, {
      header: false,
      skipEmptyLines,
      trimWhitespace,
      encoding: 'UTF-8',
      complete: (parseResult) => {
        try {
          const rows = parseResult.data as string[][];

          if (rows.length === 0) {
            result.errors.push({
              row: 0,
              field: 'קובץ',
              value: '',
              message: 'הקובץ ריק או לא תקין',
              severity: 'error'
            });
            resolve(result);
            return;
          }

          // Extract headers from first row
          const headers = rows[0];
          const headerMapping = mapHeaders(headers);

          // Check if we have required headers
          const hasFullName = Object.values(headerMapping).includes('full_name');
          const hasPhone = Object.values(headerMapping).includes('phone');

          if (!hasFullName || !hasPhone) {
            result.errors.push({
              row: 0,
              field: 'כותרות',
              value: headers.join(', '),
              message: 'חסרות כותרות חובה: שם מלא וטלפון',
              severity: 'error'
            });
            resolve(result);
            return;
          }

          // Process data rows
          const dataRows = rows.slice(1);
          result.totalRows = dataRows.length;

          // Limit rows if specified
          const rowsToProcess = dataRows.slice(0, maxRows);
          if (dataRows.length > maxRows) {
            result.errors.push({
              row: maxRows + 1,
              field: 'מגבלה',
              value: dataRows.length,
              message: `מקסימום ${maxRows} שורות מותר. ${dataRows.length - maxRows} שורות נדלגו`,
              severity: 'warning'
            });
          }

          // Track phones for duplicate detection within the file
          const phonesInFile = new Set<string>();
          const allExistingPhones = new Set([...existingPhones]);

          rowsToProcess.forEach((row, index) => {
            const rowNumber = index + 2; // +2 because we skip header and arrays are 0-indexed

            // Skip empty rows
            if (row.every(cell => !cell || cell.trim() === '')) {
              result.skippedRows++;
              return;
            }

            const validation = validateLeadRow(row, rowNumber, headerMapping, Array.from(allExistingPhones));

            if (validation.errors.length > 0) {
              result.errors.push(...validation.errors);

              // Count errors vs warnings
              validation.errors.forEach(error => {
                if (error.severity === 'error') {
                  result.summary.errorCount++;
                } else {
                  result.summary.warningCount++;
                }
              });
            }

            if (validation.isDuplicate && validation.lead) {
              result.duplicates.push({
                row: rowNumber,
                phone: validation.lead.phone,
                full_name: validation.lead.full_name
              });
              result.summary.duplicateCount++;
            } else if (validation.lead) {
              // Check for duplicates within the file itself
              if (phonesInFile.has(validation.lead.phone)) {
                result.duplicates.push({
                  row: rowNumber,
                  phone: validation.lead.phone,
                  full_name: validation.lead.full_name
                });
                result.summary.duplicateCount++;
              } else {
                phonesInFile.add(validation.lead.phone);
                allExistingPhones.add(validation.lead.phone);
                result.data.push(validation.lead);
                result.validRows++;
                result.summary.successCount++;
              }
            }
          });

          // Determine overall success
          result.success = result.summary.errorCount === 0 && result.data.length > 0;

          resolve(result);
        } catch (error) {
          result.errors.push({
            row: 0,
            field: 'עיבוד',
            value: '',
            message: `שגיאה בעיבוד הקובץ: ${error instanceof Error ? error.message : 'שגיאה לא ידועה'}`,
            severity: 'error'
          });
          resolve(result);
        }
      },
      error: (error) => {
        result.errors.push({
          row: 0,
          field: 'קריאה',
          value: '',
          message: `שגיאה בקריאת הקובץ: ${error.message}`,
          severity: 'error'
        });
        resolve(result);
      }
    });
  });
}

/**
 * Utility function to format validation errors for display
 */
export function formatValidationErrors(errors: ValidationError[]): string {
  if (errors.length === 0) return '';

  const errorsByRow = errors.reduce((acc, error) => {
    if (!acc[error.row]) {
      acc[error.row] = [];
    }
    acc[error.row].push(error);
    return acc;
  }, {} as Record<number, ValidationError[]>);

  return Object.entries(errorsByRow)
    .map(([row, rowErrors]) => {
      const errorMessages = rowErrors.map(e => `${e.field}: ${e.message}`).join(', ');
      return `שורה ${row}: ${errorMessages}`;
    })
    .join('\n');
}

/**
 * Utility function to generate import summary text
 */
export function generateImportSummary(result: ParseResult): string {
  const { summary, totalRows, validRows, skippedRows } = result;

  return `
סיכום ייבוא:
• סה"כ שורות בקובץ: ${totalRows}
• שורות תקינות: ${validRows}
• שורות שנדלגו: ${skippedRows}
• לידים שיובאו בהצלחה: ${summary.successCount}
• שגיאות: ${summary.errorCount}
• אזהרות: ${summary.warningCount}
• לידים כפולים: ${summary.duplicateCount}
  `.trim();
}
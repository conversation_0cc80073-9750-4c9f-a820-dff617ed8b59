/**
 * CSV Template Generator for Lead Import
 * Generates downloadable CSV templates with proper Hebrew headers and sample data
 */

import { LEAD_STATUSES } from '@/constants/leadStatuses';
import { LEAD_SOURCES } from '@/constants/leadSources';

export interface CSVTemplateField {
  key: string;
  hebrewHeader: string;
  englishHeader: string;
  required: boolean;
  description: string;
  example: string;
  validation?: string;
}

/**
 * CSV template field definitions with Hebrew headers and validation rules
 */
export const CSV_TEMPLATE_FIELDS: CSVTemplateField[] = [
  {
    key: 'full_name',
    hebrewHeader: 'שם מלא',
    englishHeader: 'full_name',
    required: true,
    description: 'שם מלא של הליד (חובה)',
    example: 'יוחנן כהן',
    validation: 'טקסט, מינימום 2 תווים'
  },
  {
    key: 'phone',
    hebrewHeader: 'טלפון',
    englishHeader: 'phone',
    required: true,
    description: 'מס<PERSON>ר טלפון ישראלי (חובה)',
    example: '0501234567',
    validation: 'פורמט ישראלי: 050/052/053/054/055/058-XXXXXXX או 02/03/04/08/09-XXXXXXX'
  },
  {
    key: 'email',
    hebrewHeader: 'אימייל',
    englishHeader: 'email',
    required: false,
    description: 'כתובת אימייל (אופציונלי)',
    example: '<EMAIL>',
    validation: 'פורמט אימייל תקין'
  },
  {
    key: 'source',
    hebrewHeader: 'מקור',
    englishHeader: 'source',
    required: false,
    description: 'מקור הליד (אופציונלי)',
    example: 'גוגל',
    validation: `אחד מהערכים: ${LEAD_SOURCES.join(', ')}`
  },
  {
    key: 'status',
    hebrewHeader: 'סטטוס',
    englishHeader: 'status',
    required: false,
    description: 'סטטוס הליד (אופציונלי, ברירת מחדל: "ליד חדש")',
    example: 'ליד חדש',
    validation: `אחד מהערכים: ${LEAD_STATUSES.join(', ')}`
  },
  {
    key: 'value',
    hebrewHeader: 'ערך',
    englishHeader: 'value',
    required: false,
    description: 'ערך כספי של הליד בשקלים (אופציונלי)',
    example: '5000',
    validation: 'מספר חיובי, ללא סימני מטבע'
  },
  {
    key: 'notes',
    hebrewHeader: 'הערות',
    englishHeader: 'notes',
    required: false,
    description: 'הערות נוספות על הליד (אופציונלי)',
    example: 'לקוח פוטנציאלי מעניין',
    validation: 'טקסט חופשי'
  },
  {
    key: 'assigned_user_email',
    hebrewHeader: 'אימייל משתמש מוקצה',
    englishHeader: 'assigned_user_email',
    required: false,
    description: 'אימייל של המשתמש שיוקצה לליד (אופציונלי)',
    example: '<EMAIL>',
    validation: 'אימייל של משתמש קיים במערכת'
  }
];

/**
 * Sample data for the CSV template
 */
const SAMPLE_DATA = [
  {
    full_name: 'יוחנן כהן',
    phone: '0501234567',
    email: '<EMAIL>',
    source: 'גוגל',
    status: 'ליד חדש',
    value: '5000',
    notes: 'לקוח פוטנציאלי מעניין',
    assigned_user_email: '<EMAIL>'
  },
  {
    full_name: 'שרה לוי',
    phone: '0529876543',
    email: '<EMAIL>',
    source: 'פייסבוק',
    status: 'צריך פולואפ',
    value: '3000',
    notes: 'התעניינה בשירותי ייעוץ',
    assigned_user_email: '<EMAIL>'
  },
  {
    full_name: 'דוד ישראלי',
    phone: '0541112233',
    email: '',
    source: 'המלצה',
    status: 'ליד חדש',
    value: '',
    notes: 'הגיע דרך המלצה של לקוח קיים',
    assigned_user_email: ''
  }
];

/**
 * Converts array of objects to CSV string
 */
function arrayToCSV(data: any[], headers: string[]): string {
  const csvHeaders = headers.join(',');
  const csvRows = data.map(row =>
    headers.map(header => {
      const value = row[header] || '';
      // Escape quotes and wrap in quotes if contains comma, quote, or newline
      if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    }).join(',')
  );

  return [csvHeaders, ...csvRows].join('\n');
}

/**
 * Generates CSV template content with Hebrew headers and sample data
 */
export function generateCSVTemplate(): string {
  const headers = CSV_TEMPLATE_FIELDS.map(field => field.englishHeader);
  return arrayToCSV(SAMPLE_DATA, headers);
}

/**
 * Generates CSV template with Hebrew headers for better user experience
 */
export function generateCSVTemplateWithHebrewHeaders(): string {
  const hebrewHeaders = CSV_TEMPLATE_FIELDS.map(field => field.hebrewHeader);
  const dataWithHebrewHeaders = SAMPLE_DATA.map(row => {
    const hebrewRow: any = {};
    CSV_TEMPLATE_FIELDS.forEach(field => {
      hebrewRow[field.hebrewHeader] = row[field.key as keyof typeof row];
    });
    return hebrewRow;
  });

  return arrayToCSV(dataWithHebrewHeaders, hebrewHeaders);
}

/**
 * Downloads CSV template file
 */
export function downloadCSVTemplate(useHebrewHeaders: boolean = true): void {
  const csvContent = useHebrewHeaders ? generateCSVTemplateWithHebrewHeaders() : generateCSVTemplate();
  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'leads_import_template.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

/**
 * Generates validation rules documentation
 */
export function generateValidationRulesText(): string {
  const rules = CSV_TEMPLATE_FIELDS.map(field => {
    const requiredText = field.required ? '(חובה)' : '(אופציונלי)';
    return `${field.hebrewHeader} ${requiredText}:
  - תיאור: ${field.description}
  - דוגמה: ${field.example}
  - כללי תקינות: ${field.validation || 'ללא הגבלות מיוחדות'}`;
  }).join('\n\n');

  return `כללי תקינות לייבוא לידים מקובץ CSV:

${rules}

הערות כלליות:
- הקובץ חייב להיות בפורמט CSV (Comma Separated Values)
- השורה הראשונה חייבת להכיל כותרות עמודות
- ניתן להשתמש בכותרות בעברית או באנגלית
- שדות חובה: שם מלא וטלפון
- לידים כפולים (לפי מספר טלפון) יידחו אוטומטית
- מקסימום 1000 לידים בייבוא אחד
- גודל קובץ מקסימלי: 5MB`;
}
/**
 * Phone number formatting utilities for Israeli phone numbers
 */

/**
 * Format phone number to E.164 international format for Twilio
 * @param phone - Phone number in any format
 * @returns Phone number in E.164 format (+972XXXXXXXXX)
 */
export function formatPhoneToE164(phone: string): string {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // If it starts with 972 (Israel country code), it's already in international format
  if (cleaned.startsWith('972')) {
    return `+${cleaned}`;
  }
  
  // If it starts with 0, replace with 972 (Israeli number)
  if (cleaned.startsWith('0')) {
    return `+972${cleaned.substring(1)}`;
  }
  
  // If it's a 9-digit number, assume it's Israeli without the leading 0
  if (cleaned.length === 9) {
    return `+972${cleaned}`;
  }
  
  // If it's a 10-digit number starting with 05, assume it's Israeli
  if (cleaned.length === 10 && cleaned.startsWith('05')) {
    return `+972${cleaned.substring(1)}`;
  }
  
  // Otherwise, assume it needs +972 prefix
  return `+972${cleaned}`;
}

/**
 * Format phone number for display (Israeli format)
 * @param phone - Phone number in any format
 * @returns Phone number formatted for display
 */
export function formatPhoneForDisplay(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');

  // If it starts with 972, format as +972-XX-XXX-XXXX
  if (cleaned.startsWith('972')) {
    const number = cleaned.substring(3);
    if (number.length >= 9) {
      return `+972-${number.substring(0, 2)}-${number.substring(2, 5)}-${number.substring(5)}`;
    }
    return `+972-${number}`;
  }

  // If it starts with 0, format as 0XX-XXX-XXXX
  if (cleaned.startsWith('0') && cleaned.length >= 10) {
    return `${cleaned.substring(0, 3)}-${cleaned.substring(3, 6)}-${cleaned.substring(6)}`;
  }

  return phone;
}

/**
 * Format phone number for WhatsApp display (consistent international format)
 * Always shows phone numbers in +972-XX-XXX-XXXX format for consistency
 * @param phone - Phone number in any format
 * @returns Phone number formatted for WhatsApp display
 */
export function formatPhoneForWhatsAppDisplay(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');

  // Normalize to 972 format first
  let normalized = '';
  if (cleaned.startsWith('0') && cleaned.length === 10) {
    // Israeli local format (0xxxxxxxxx) -> international (972xxxxxxxxx)
    normalized = '972' + cleaned.substring(1);
  } else if (cleaned.startsWith('972')) {
    // Already in international format
    normalized = cleaned;
  } else if (cleaned.length === 9) {
    // 9 digits, assume Israeli mobile and add 972
    normalized = '972' + cleaned;
  } else {
    // Return as-is if we can't normalize
    return phone;
  }

  // Format as +972-XX-XXX-XXXX
  if (normalized.startsWith('972') && normalized.length === 12) {
    const number = normalized.substring(3);
    return `+972-${number.substring(0, 2)}-${number.substring(2, 5)}-${number.substring(5)}`;
  }

  return phone;
}

/**
 * Validate if phone number is a valid Israeli phone number
 * @param phone - Phone number to validate
 * @returns true if valid Israeli phone number
 */
export function isValidIsraeliPhone(phone: string): boolean {
  const cleaned = phone.replace(/\D/g, '');
  
  // Check if it's a valid Israeli number format
  if (cleaned.startsWith('972')) {
    // International format: should be 12 digits total (972 + 9 digits)
    return cleaned.length === 12;
  }
  
  if (cleaned.startsWith('0')) {
    // Local format: should be 10 digits total (0 + 9 digits)
    return cleaned.length === 10;
  }
  
  // 9 digits without prefix
  return cleaned.length === 9;
}

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface UserSyncRequest {
  action: 'sync' | 'delete' | 'list-orphaned' | 'cleanup-orphaned';
  userId?: string;
  companyId?: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const body: UserSyncRequest = await req.json();
    const { action, userId, companyId } = body;

    console.log('User management action:', action, { userId, companyId });

    switch (action) {
      case 'list-orphaned':
        return await listOrphanedUsers(supabaseAdmin);
      
      case 'sync':
        return await syncUsers(supabaseAdmin, companyId);
      
      case 'delete':
        if (!userId) {
          return new Response(
            JSON.stringify({ success: false, error: 'userId required for delete action' }),
            { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
        }
        return await deleteUserCompletely(supabaseAdmin, userId);
      
      case 'cleanup-orphaned':
        return await cleanupOrphanedUsers(supabaseAdmin);
      
      default:
        return new Response(
          JSON.stringify({ success: false, error: 'Invalid action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }
  } catch (error) {
    console.error('User management error:', error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
};

// List users who exist in auth but not in user_roles
async function listOrphanedUsers(supabaseAdmin: any) {
  console.log('Listing orphaned users...');
  
  // Get all auth users
  const { data: authUsers, error: authError } = await supabaseAdmin.auth.admin.listUsers();
  if (authError) {
    throw new Error('Failed to list auth users: ' + authError.message);
  }

  // Get all users with roles
  const { data: userRoles, error: rolesError } = await supabaseAdmin
    .from('user_roles')
    .select('user_id');
  
  if (rolesError) {
    throw new Error('Failed to list user roles: ' + rolesError.message);
  }

  const usersWithRoles = new Set(userRoles.map((role: any) => role.user_id));
  
  const orphanedUsers = authUsers.users.filter(user => !usersWithRoles.has(user.id));

  console.log(`Found ${orphanedUsers.length} orphaned users`);

  return new Response(
    JSON.stringify({
      success: true,
      orphanedUsers: orphanedUsers.map(user => ({
        id: user.id,
        email: user.email,
        created_at: user.created_at,
        last_sign_in_at: user.last_sign_in_at
      }))
    }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

// Sync all auth users with user_roles for a specific company or get global stats
async function syncUsers(supabaseAdmin: any, companyId?: string) {
  console.log('Getting sync stats for:', companyId);

  // Get all auth users
  const { data: authUsers, error: authError } = await supabaseAdmin.auth.admin.listUsers();
  if (authError) {
    throw new Error('Failed to list auth users: ' + authError.message);
  }

  if (companyId && companyId !== 'all') {
    // Get existing user roles for this specific company
    const { data: existingRoles, error: rolesError } = await supabaseAdmin
      .from('user_roles')
      .select('user_id')
      .eq('company_id', companyId);

    if (rolesError) {
      throw new Error('Failed to list existing roles: ' + rolesError.message);
    }

    const existingUserIds = new Set(existingRoles.map((role: any) => role.user_id));

    return new Response(
      JSON.stringify({
        success: true,
        totalAuthUsers: authUsers.users.length,
        usersInCompany: existingRoles.length,
        usersWithoutRoles: authUsers.users.filter(user => !existingUserIds.has(user.id)).length
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } else {
    // Get global stats - all user roles across all companies
    const { data: allRoles, error: rolesError } = await supabaseAdmin
      .from('user_roles')
      .select('user_id');

    if (rolesError) {
      throw new Error('Failed to list all roles: ' + rolesError.message);
    }

    const usersWithRoles = new Set(allRoles.map((role: any) => role.user_id));

    return new Response(
      JSON.stringify({
        success: true,
        totalAuthUsers: authUsers.users.length,
        usersInCompany: usersWithRoles.size, // Unique users with roles
        usersWithoutRoles: authUsers.users.filter(user => !usersWithRoles.has(user.id)).length
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

// Delete user completely from both auth and user_roles
async function deleteUserCompletely(supabaseAdmin: any, userId: string) {
  console.log('Deleting user completely:', userId);

  try {
    // Step 1: Handle foreign key references that don't have CASCADE
    console.log('Cleaning up foreign key references...');

    // Update cases.assigned_user_id to NULL (unassign cases)
    const { error: casesError } = await supabaseAdmin
      .from('cases')
      .update({ assigned_user_id: null })
      .eq('assigned_user_id', userId);

    if (casesError) {
      console.error('Error updating cases assigned_user_id:', casesError);
    } else {
      console.log('Updated cases to remove user assignment');
    }

    // Update companies.onboarded_by to NULL
    const { error: companiesError } = await supabaseAdmin
      .from('companies')
      .update({ onboarded_by: null })
      .eq('onboarded_by', userId);

    if (companiesError) {
      console.error('Error updating companies onboarded_by:', companiesError);
    } else {
      console.log('Updated companies to remove onboarded_by reference');
    }

    // Update lead_answer_status.created_by to NULL
    const { error: leadAnswerError } = await supabaseAdmin
      .from('lead_answer_status')
      .update({ created_by: null })
      .eq('created_by', userId);

    if (leadAnswerError) {
      console.error('Error updating lead_answer_status created_by:', leadAnswerError);
    } else {
      console.log('Updated lead_answer_status to remove created_by reference');
    }

    // Update leads.assigned_user_id to NULL (unassign leads)
    const { error: leadsAssignedError } = await supabaseAdmin
      .from('leads')
      .update({ assigned_user_id: null })
      .eq('assigned_user_id', userId);

    if (leadsAssignedError) {
      console.error('Error updating leads assigned_user_id:', leadsAssignedError);
    } else {
      console.log('Updated leads to remove user assignment');
    }

    // Step 2: Delete from user_roles
    console.log('Deleting user roles...');
    const { error: roleError } = await supabaseAdmin
      .from('user_roles')
      .delete()
      .eq('user_id', userId);

    if (roleError) {
      console.error('Error deleting user roles:', roleError);
      throw new Error('Failed to delete user roles: ' + roleError.message);
    } else {
      console.log('Successfully deleted user roles');
    }

    // Step 3: Delete from auth (this will cascade delete other references)
    console.log('Deleting user from auth...');
    const { error: authError } = await supabaseAdmin.auth.admin.deleteUser(userId);

    if (authError) {
      console.error('Error deleting user from auth:', authError);
      throw new Error('Failed to delete user from auth: ' + authError.message);
    }

    console.log('User deleted successfully from both auth and all related tables');

    return new Response(
      JSON.stringify({
        success: true,
        message: 'User deleted completely from auth and all related tables'
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error in complete user deletion:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Failed to delete user completely'
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

// Clean up all orphaned users (delete from auth if no roles)
async function cleanupOrphanedUsers(supabaseAdmin: any) {
  console.log('Cleaning up orphaned users...');
  
  const orphanedResponse = await listOrphanedUsers(supabaseAdmin);
  const orphanedData = await orphanedResponse.json();
  
  if (!orphanedData.success) {
    throw new Error('Failed to get orphaned users');
  }

  const orphanedUsers = orphanedData.orphanedUsers;
  let deletedCount = 0;
  const errors: string[] = [];

  for (const user of orphanedUsers) {
    try {
      const { error } = await supabaseAdmin.auth.admin.deleteUser(user.id);
      if (error) {
        errors.push(`Failed to delete ${user.email}: ${error.message}`);
      } else {
        deletedCount++;
        console.log(`Deleted orphaned user: ${user.email}`);
      }
    } catch (error) {
      errors.push(`Failed to delete ${user.email}: ${error.message}`);
    }
  }

  return new Response(
    JSON.stringify({
      success: true,
      deletedCount,
      totalOrphaned: orphanedUsers.length,
      errors
    }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

serve(handler);
